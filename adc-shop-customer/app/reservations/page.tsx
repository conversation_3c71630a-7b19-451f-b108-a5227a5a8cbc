"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Users, MapPin, Edit, X, Eye, Plus } from "lucide-react";
import Header from "@/components/Header";
import { useReservations, Reservation } from "@/lib/context/ReservationContext";
import { useRouter } from "next/navigation";

export default function ReservationsPage() {
  const { reservations, cancelReservation, updateReservation } = useReservations();
  const router = useRouter();

  const getStatusBadge = (status: Reservation['status']) => {
    const statusConfig = {
      confirmed: {
        variant: "default" as const,
        className: "bg-green-100 text-green-800 border-green-200 hover:bg-green-100",
        icon: <Calendar className="h-3 w-3" />,
        text: "Confirmed"
      },
      pending: {
        variant: "secondary" as const,
        className: "bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-100",
        icon: <Clock className="h-3 w-3" />,
        text: "Pending"
      },
      cancelled: {
        variant: "destructive" as const,
        className: "bg-red-100 text-red-800 border-red-200 hover:bg-red-100",
        icon: <X className="h-3 w-3" />,
        text: "Cancelled"
      },
      completed: {
        variant: "outline" as const,
        className: "bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-100",
        icon: <Calendar className="h-3 w-3" />,
        text: "Completed"
      }
    };

    const config = statusConfig[status];
    return (
      <Badge variant={config.variant} className={`flex items-center gap-1 ${config.className}`}>
        {config.icon}
        {config.text}
      </Badge>
    );
  };

  const getActionButton = (reservation: Reservation) => {
    switch (reservation.status) {
      case 'confirmed':
        return (
          <Button
            variant="ghost"
            size="sm"
            className="text-primary hover:text-primary/80"
            onClick={() => handleAction(reservation.id, 'modify')}
          >
            <Edit className="h-4 w-4 mr-1" />
            Modify
          </Button>
        );
      case 'pending':
        return (
          <Button
            variant="ghost"
            size="sm"
            className="text-destructive hover:text-destructive/80"
            onClick={() => handleAction(reservation.id, 'cancel')}
          >
            <X className="h-4 w-4 mr-1" />
            Cancel
          </Button>
        );
      case 'cancelled':
      case 'completed':
        return (
          <Button
            variant="ghost"
            size="sm"
            className="text-muted-foreground hover:text-foreground"
            onClick={() => handleAction(reservation.id, 'view')}
          >
            <Eye className="h-4 w-4 mr-1" />
            View Details
          </Button>
        );
    }
  };

  const handleAction = (reservationId: string, action: string) => {
    switch (action) {
      case 'cancel':
        if (window.confirm('Are you sure you want to cancel this reservation?')) {
          cancelReservation(reservationId);
        }
        break;
      case 'modify':
        // In a real app, this would open a modal or navigate to edit page
        console.log(`Modify reservation ${reservationId}`);
        break;
      case 'view':
        router.push(`/reservations/${reservationId}`);
        break;
      default:
        console.log(`${action} reservation ${reservationId}`);
    }
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <p className="text-foreground tracking-light text-[32px] font-bold leading-tight min-w-72">
                Your Reservations
              </p>
              <Button className="bg-primary text-primary-foreground hover:bg-primary/90">
                <Plus className="h-4 w-4 mr-2" />
                New Reservation
              </Button>
            </div>

            {/* Reservations Table */}
            <div className="px-4 py-3">
              <div className="flex overflow-hidden rounded-xl border border-border bg-card">
                <div className="flex-1 overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-muted/50">
                        <th className="px-4 py-3 text-left text-foreground text-sm font-medium leading-normal min-w-[120px]">
                          Date
                        </th>
                        <th className="px-4 py-3 text-left text-foreground text-sm font-medium leading-normal min-w-[100px]">
                          Time
                        </th>
                        <th className="px-4 py-3 text-left text-foreground text-sm font-medium leading-normal min-w-[180px]">
                          Restaurant
                        </th>
                        <th className="px-4 py-3 text-left text-foreground text-sm font-medium leading-normal min-w-[100px]">
                          Party Size
                        </th>
                        <th className="px-4 py-3 text-left text-foreground text-sm font-medium leading-normal min-w-[120px]">
                          Status
                        </th>
                        <th className="px-4 py-3 text-left text-foreground text-sm font-medium leading-normal min-w-[120px]">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {reservations.map((reservation) => (
                        <tr key={reservation.id} className="border-t border-border hover:bg-muted/30 transition-colors">
                          <td className="px-4 py-4 text-muted-foreground text-sm font-normal leading-normal">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              {reservation.date}
                            </div>
                          </td>
                          <td className="px-4 py-4 text-muted-foreground text-sm font-normal leading-normal">
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              {reservation.time}
                            </div>
                          </td>
                          <td className="px-4 py-4 text-foreground text-sm font-normal leading-normal">
                            <div className="flex flex-col">
                              <button
                                onClick={() => router.push(`/reservations/${reservation.id}`)}
                                className="font-medium text-left hover:text-primary transition-colors"
                              >
                                {reservation.restaurant}
                              </button>
                              {reservation.location && (
                                <span className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                                  <MapPin className="h-3 w-3" />
                                  {reservation.location}
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-4 text-muted-foreground text-sm font-normal leading-normal">
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4" />
                              {reservation.partySize}
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            {getStatusBadge(reservation.status)}
                          </td>
                          <td className="px-4 py-4">
                            {getActionButton(reservation)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 px-4 py-6">
              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="bg-green-100 p-2 rounded-full">
                    <Calendar className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Confirmed</p>
                    <p className="text-lg font-bold text-foreground">
                      {reservations.filter(r => r.status === 'confirmed').length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="bg-yellow-100 p-2 rounded-full">
                    <Clock className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Pending</p>
                    <p className="text-lg font-bold text-foreground">
                      {reservations.filter(r => r.status === 'pending').length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Calendar className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Completed</p>
                    <p className="text-lg font-bold text-foreground">
                      {reservations.filter(r => r.status === 'completed').length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="bg-red-100 p-2 rounded-full">
                    <X className="h-4 w-4 text-red-600" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Cancelled</p>
                    <p className="text-lg font-bold text-foreground">
                      {reservations.filter(r => r.status === 'cancelled').length}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
